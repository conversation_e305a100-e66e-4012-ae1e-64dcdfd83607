# 🏎️ F1 Simulators - Experiencia de Carreras Profesional

Una web moderna y atractiva para un centro de simuladores de Fórmula 1, construida con HTML5, CSS3, JavaScript y Bootstrap.

## 🚀 Características

### Diseño y UX
- **Diseño responsivo** que se adapta a todos los dispositivos
- **Tema F1 personalizado** con colores oficiales (rojo, negro, dorado)
- **Animaciones suaves** y efectos visuales atractivos
- **Navegación intuitiva** con scroll suave entre secciones

### Secciones Principales
1. **Hero Section** - Presentación impactante con call-to-action
2. **Simuladores** - Descripción de la tecnología y equipamiento
3. **Experiencias** - Diferentes modalidades de uso con detalles y precios
4. **Nosotros** - Historia y evolución del centro
5. **Equipo** - Presentación del equipo profesional
6. **Contacto** - Formulario de contacto funcional

### Experiencias Disponibles
- **Sesión Individual** - Práctica personal ($25 USD)
- **Carreras Grupales** - Competencia multijugador ($40 USD)
- **Campeonatos** - Torneos oficiales ($150 USD)
- **Eventos Corporativos** - Team building ($35 USD)
- **Entrenamiento Pro** - Coaching profesional ($80 USD)
- **Fiestas & Celebraciones** - Eventos especiales ($45 USD)

### Funcionalidades Técnicas
- **Efectos de sonido** simulando motores F1
- **Partículas animadas** en el hero section
- **Hover effects** en cards y elementos interactivos
- **Formulario de contacto** con validación
- **Modales informativos** para cada experiencia

## 🛠️ Tecnologías Utilizadas

- **HTML5** - Estructura semántica
- **CSS3** - Estilos avanzados con gradientes y animaciones
- **JavaScript ES6+** - Interactividad y efectos
- **Bootstrap 5** - Framework CSS responsivo
- **Font Awesome** - Iconografía
- **Google Fonts** - Tipografías (Montserrat, Roboto Slab)

## 📁 Estructura del Proyecto

```
front-web/
├── index.html          # Página principal
├── css/
│   └── styles.css      # Estilos personalizados + Bootstrap
├── js/
│   └── scripts.js      # JavaScript personalizado
├── assets/
│   ├── favicon.ico     # Icono del sitio
│   └── img/            # Imágenes y recursos gráficos
├── README.md           # Documentación
└── Pront.md           # Notas del proyecto
```

## 🎨 Paleta de Colores F1

- **Rojo F1**: `#dc143c` - Color principal de la marca
- **Negro F1**: `#000000` - Color secundario elegante
- **Dorado F1**: `#ffd700` - Acentos y elementos destacados
- **Plata F1**: `#c0c0c0` - Detalles metálicos
- **Blanco**: `#ffffff` - Texto y fondos

## 🚀 Instalación y Uso

1. **Clonar o descargar** el proyecto
2. **Abrir** `index.html` en un navegador web
3. **¡Listo!** - No requiere servidor web para funcionar

### Para desarrollo:
```bash
# Servir con un servidor local (opcional)
python -m http.server 8000
# o
npx serve .
```

## 📱 Compatibilidad

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Dispositivos móviles (iOS/Android)

## 🎯 Características Destacadas

### Experiencia de Usuario
- **Carga rápida** - Optimizado para rendimiento
- **Accesibilidad** - Cumple estándares WCAG
- **SEO friendly** - Meta tags optimizados
- **Mobile first** - Diseño pensado para móviles

### Efectos Visuales
- **Gradientes F1** en botones y fondos
- **Animaciones CSS** suaves y profesionales
- **Hover effects** interactivos
- **Partículas flotantes** en el hero section

### Funcionalidades Interactivas
- **Sonidos de motor F1** en botones principales
- **Scroll suave** entre secciones
- **Modales informativos** con detalles de cada experiencia
- **Formulario validado** con mensajes en español

## 🔧 Personalización

### Cambiar colores:
Editar las variables CSS en `css/styles.css`:
```css
:root {
  --f1-red: #dc143c;
  --f1-black: #000000;
  --f1-gold: #ffd700;
}
```

### Agregar nuevas experiencias:
1. Duplicar una card en la sección "Experiencias"
2. Crear el modal correspondiente
3. Actualizar los enlaces y contenido

### Modificar contenido:
Todo el texto está en español y puede editarse directamente en `index.html`

## 📞 Soporte

Para dudas o sugerencias sobre este proyecto, puedes:
- Revisar el código fuente
- Modificar según tus necesidades
- Adaptar para otros tipos de simuladores

## 📄 Licencia

Este proyecto está basado en el tema Agency de Start Bootstrap (MIT License) con modificaciones personalizadas para simuladores F1.

---

**¡Desarrollado con ❤️ para los amantes de la Fórmula 1!** 🏁
