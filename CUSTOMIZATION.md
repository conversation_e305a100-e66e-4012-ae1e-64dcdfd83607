# 🎨 Guía de Personalización - F1 Simulators

Esta guía te ayudará a personalizar y expandir tu sitio web de simuladores F1.

## 🎯 Cambios Rápidos

### 1. Información de Contacto
Edita en `index.html` la sección de contacto:
```html
<!-- Cambiar información de contacto -->
<input class="form-control" id="phone" type="tel" placeholder="Tu Teléfono *" />
```

### 2. Precios y Servicios
Modifica los precios en los modales de experiencias:
```html
<li><strong>Precio:</strong> $25 USD</li>
```

### 3. Información del Equipo
Actualiza nombres y roles en la sección equipo:
```html
<h4><PERSON></h4>
<p class="text-muted">Director <PERSON><PERSON></p>
```

## 🎨 Personalización Visual

### Cambiar Colores Principales
En `css/styles.css`, modifica las variables:
```css
:root {
  --f1-red: #dc143c;      /* <PERSON><PERSON><PERSON> principal */
  --f1-black: #000000;    /* Negro elegante */
  --f1-gold: #ffd700;     /* Dorado para acentos */
  --f1-silver: #c0c0c0;   /* Plata para detalles */
}
```

### Personalizar Gradientes
```css
.masthead {
  background: linear-gradient(135deg, tu-color-1, tu-color-2) !important;
}
```

### Cambiar Fuentes
Reemplaza en el `<head>` de `index.html`:
```html
<link href="https://fonts.googleapis.com/css?family=TU-FUENTE" rel="stylesheet" />
```

## 🖼️ Gestión de Imágenes

### Estructura de Imágenes Recomendada
```
assets/img/
├── portfolio/          # Imágenes de experiencias (6 imágenes)
├── about/             # Timeline (4 imágenes)
├── team/              # Fotos del equipo (3 imágenes)
├── logos/             # Logos de sponsors/partners
└── backgrounds/       # Fondos adicionales
```

### Tamaños Recomendados
- **Portfolio**: 650x350px (ratio 1.86:1)
- **About/Timeline**: 300x300px (cuadradas)
- **Team**: 300x300px (cuadradas)
- **Logos**: 200x100px (ratio 2:1)

## 📝 Agregar Nuevas Experiencias

### 1. Crear la Card
```html
<div class="col-lg-4 col-sm-6 mb-4">
    <div class="portfolio-item">
        <a class="portfolio-link" data-bs-toggle="modal" href="#nuevaExperienciaModal">
            <div class="portfolio-hover">
                <div class="portfolio-hover-content"><i class="fas fa-plus fa-3x"></i></div>
            </div>
            <img class="img-fluid" src="assets/img/portfolio/nueva.jpg" alt="..." />
        </a>
        <div class="portfolio-caption">
            <div class="portfolio-caption-heading">Nueva Experiencia</div>
            <div class="portfolio-caption-subheading text-muted">Descripción Corta</div>
        </div>
    </div>
</div>
```

### 2. Crear el Modal
```html
<div class="portfolio-modal modal fade" id="nuevaExperienciaModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="close-modal" data-bs-dismiss="modal">
                <img src="assets/img/close-icon.svg" alt="Close modal" />
            </div>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="modal-body">
                            <h2 class="text-uppercase">Nueva Experiencia</h2>
                            <p class="item-intro text-muted">Descripción breve.</p>
                            <img class="img-fluid d-block mx-auto" src="assets/img/portfolio/nueva.jpg" alt="..." />
                            <p>Descripción detallada de la nueva experiencia...</p>
                            <ul class="list-inline">
                                <li><strong>Duración:</strong> X minutos</li>
                                <li><strong>Incluye:</strong> Lista de incluidos</li>
                                <li><strong>Precio:</strong> $X USD</li>
                            </ul>
                            <button class="btn btn-primary btn-xl text-uppercase" data-bs-dismiss="modal" type="button">
                                <i class="fas fa-xmark me-1"></i>
                                Cerrar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

## 🔧 Funcionalidades Avanzadas

### Agregar Google Analytics
En el `<head>` de `index.html`:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

### Integrar WhatsApp
Agregar botón flotante de WhatsApp:
```html
<a href="https://wa.me/1234567890" class="whatsapp-float" target="_blank">
    <i class="fab fa-whatsapp"></i>
</a>
```

```css
.whatsapp-float {
    position: fixed;
    width: 60px;
    height: 60px;
    bottom: 40px;
    right: 40px;
    background-color: #25d366;
    color: #FFF;
    border-radius: 50px;
    text-align: center;
    font-size: 30px;
    box-shadow: 2px 2px 3px #999;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}
```

### Sistema de Reservas
Para integrar un sistema de reservas, puedes:
1. **Calendly**: Embeber widget de Calendly
2. **Google Calendar**: Integrar con Google Calendar API
3. **Sistema personalizado**: Desarrollar con PHP/Node.js

## 📱 Optimización Móvil

### Mejorar Performance
```html
<!-- Preload de recursos críticos -->
<link rel="preload" href="css/styles.css" as="style">
<link rel="preload" href="js/scripts.js" as="script">
```

### PWA (Progressive Web App)
Crear `manifest.json`:
```json
{
  "name": "F1 Simulators",
  "short_name": "F1Sim",
  "description": "Experiencia de simuladores F1",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#000000",
  "theme_color": "#dc143c",
  "icons": [
    {
      "src": "assets/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
```

## 🌐 SEO y Marketing

### Meta Tags Adicionales
```html
<meta property="og:title" content="F1 Simulators - Experiencia de Carreras">
<meta property="og:description" content="Vive la adrenalina de la F1 con nuestros simuladores profesionales">
<meta property="og:image" content="assets/img/og-image.jpg">
<meta property="og:url" content="https://tu-dominio.com">
```

### Schema Markup
```html
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "name": "F1 Simulators",
  "description": "Centro de simuladores de Fórmula 1",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Tu dirección",
    "addressLocality": "Tu ciudad",
    "addressCountry": "Tu país"
  }
}
</script>
```

## 🚀 Deployment

### GitHub Pages
1. Sube el código a GitHub
2. Ve a Settings > Pages
3. Selecciona la rama main
4. Tu sitio estará en `https://usuario.github.io/repositorio`

### Netlify
1. Conecta tu repositorio de GitHub
2. Deploy automático en cada commit
3. Dominio personalizado disponible

### Hosting Tradicional
1. Sube todos los archivos vía FTP
2. Asegúrate de que `index.html` esté en la raíz
3. Configura el dominio

## 🔍 Testing y Debugging

### Herramientas Recomendadas
- **Chrome DevTools**: Para debugging
- **Lighthouse**: Para performance y SEO
- **GTmetrix**: Para velocidad de carga
- **Wave**: Para accesibilidad

### Checklist de Lanzamiento
- [ ] Todas las imágenes optimizadas
- [ ] Enlaces funcionando correctamente
- [ ] Formulario de contacto probado
- [ ] Responsive en todos los dispositivos
- [ ] SEO básico implementado
- [ ] Analytics configurado

---

¡Con esta guía podrás personalizar completamente tu sitio de simuladores F1! 🏎️
